import requests

# Lista de URLs .jpg
urls = [
    "https://ae-pic-a1.aliexpress-media.com/kf/S5c6a4df475d24ea2907ccaa11acbb569y.jpg",
    "https://ae-pic-a1.aliexpress-media.com/kf/S990fc4381f704f5e9aab42177cc319e0B.jpg",
    "https://ae-pic-a1.aliexpress-media.com/kf/S8fbf7590297b45f6869e3c86615ee15cl.jpg",
    "https://ae-pic-a1.aliexpress-media.com/kf/Sd18b49138ff04ade941b26ac3b10df7bK.jpg",
    "https://ae-pic-a1.aliexpress-media.com/kf/S208fccfd71bf40d4a1e28a841b517b1ap.jpg",
    "https://ae-pic-a1.aliexpress-media.com/kf/S78fdd72210f54fcaab1e6182a10b22363.jpg",
    "https://ae-pic-a1.aliexpress-media.com/kf/Sad418c85e28a4d33871db1b2c8a93b37c.jpg",
    "https://ae-pic-a1.aliexpress-media.com/kf/S5bff68c96ff2422a9027b5053d9eec49r.jpg",
    "https://ae-pic-a1.aliexpress-media.com/kf/S4c92e0660ded43d19f14f6877a677cf52.jpg",
    "https://ae-pic-a1.aliexpress-media.com/kf/Se560a54cc5c8483f89c9f7eb768e0939f.jpg",
    "https://ae-pic-a1.aliexpress-media.com/kf/Sf88a1a9df45041c1b9d69b711af0f4e3F.jpg",
    "https://ae-pic-a1.aliexpress-media.com/kf/S853be6beea1f4a859f3915e0520e9c48m.jpg",
    "https://ae-pic-a1.aliexpress-media.com/kf/Se478d3f870b742a2bbd246701230c322M.jpg",
    "https://ae-pic-a1.aliexpress-media.com/kf/Sd52ca8e0eef4443299b724525d6a9eafI.jpg",
    "https://ae-pic-a1.aliexpress-media.com/kf/S861ac8f513f84d389b24e74f6a60ba45K.jpg",
    "https://ae-pic-a1.aliexpress-media.com/kf/S8e4562eaa4aa4f72a6590d1b1a3caf0dl.jpg",
    "https://ae-pic-a1.aliexpress-media.com/kf/S30b24ff6aaab47248952e309de4d33dcU.jpg",
    "https://ae-pic-a1.aliexpress-media.com/kf/S766af50efd584f2e929cbedb1a530b36R.jpg"
]

# Descargar las imágenes
for url in urls:
    filename = url.split("/")[-1]
    print(f"Descargando {filename}...")
    response = requests.get(url)
    if response.status_code == 200:
        with open(filename, 'wb') as f:
            f.write(response.content)
    else:
        print(f"Error al descargar {filename}")

